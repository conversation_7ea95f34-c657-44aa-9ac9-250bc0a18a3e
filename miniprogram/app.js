// app.js
import storage from './library/extend/storage.js';
import util from './library/extend/util.js';

const { default: logger } = require('./library/extend/log.js');
const { default: updateManager } = require('./library/manager/update_manager.js');
import { getDateToday } from './library/extend/timeFormat';
const config = require('./config/index');

import promisify from './library/optimizer/promisify.js';
const { default: debounce } = require('./library/optimizer/debounce.js');
const { default: throttle } = require('./library/optimizer/throttle.js');

const preloadAssetsData = require('./resource/preloadAssets/index.js');

Page = require('./library/extend/page.js').default; // 劫持page对象

App({
  globalData: {
    navigatorInfo: {},
    windowInfo: {},
    accountInfo: {},
    menuButtonInfo: {},
  },
  onLaunch(options) {
    console.log(options);
    this.preloadAssets();
    wx.$storage.setStorageSync('platform', wx.getDeviceInfo().platform);
  },
  onShow() {
    global.state = 'show';
    updateManager.execute();
  },

  onHide() {
    global.state = 'hide';
  },

  onUnhandledRejection(res) {
    logger.error(res.reason);
  },
  onError(error) {
    logger.error(error);
  },
  onPageNotFound(res) {
    logger.error(res);
  },
  preloadAssets() {
    if (wx.canIUse('preloadAssets')) {
      wx.preloadAssets({
        data: preloadAssetsData.data,
        success(resp) {
          console.log('preloadAssets success', resp);
        },
        fail(err) {
          console.log('preloadAssets fail', err);
        },
      });
    }

    const mb = wx.getMenuButtonBoundingClientRect();
    const sh = wx.getWindowInfo().statusBarHeight;
    const distance = mb.top - sh;
    const navigator = {
      top: sh,
      bottom: sh + mb.height + distance * 2,
      left: 0,
      right: 0,
      height: mb.height + distance * 2,
      width: wx.getWindowInfo().screenWidth,
    };
    this.globalData.navigatorInfo = navigator;
    this.globalData.windowInfo = wx.getWindowInfo();
    this.globalData.accountInfo = wx.getAccountInfoSync();
    this.globalData.menuButtonInfo = wx.getMenuButtonBoundingClientRect();

    wx.$storage = new storage();
    wx.$config = config;
    wx.$promisify = promisify;
    wx.$debounce = debounce;
    wx.$throttle = throttle;
    wx.$util = util;
  },
});
