.page {
  padding: 0 20px 20px;
}

.tix {
  margin: 10px;
  padding: 20rpx;
  background: #f7e6cf;
  color: RGB(255, 165, 0);
  border-radius: 20rpx;
  font-size: 28rpx;
}

.search-container {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.search-wrapper {
  flex: 1;
}

.add-button {
  margin-left: 10px;
}

.no-data {
  text-align: center;
  color: #999;
}

.terminal-item {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.terminal-info {
  padding: 5px;
}

.item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px; /* 每项之间的间距 */
}

.label {
  color: #666; /* 标题颜色 */
  font-size: 14px; /* 标题字号 */
  width: 90px; /* 固定宽度，确保对齐 */
  flex-shrink: 0; /* 防止内容过长时挤压标题 */
}

.value {
  flex-grow: 1; /* 占满剩余空间 */
  color: #333; /* 内容颜色 */
  font-size: 14px; /* 内容字号 */
  word-break: break-word; /* 自动换行 */
}
