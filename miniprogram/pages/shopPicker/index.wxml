<view class="tix">
  <view>该列表可查询已在‘泰博出行商家版’小程序注册的门店。未注册的门店，请引导老板前往小程序完成注册。</view>
</view>
<view class="page">
  <t-sticky>
    <view class="search-container">
      <view class="search-wrapper">
        <t-search
          placeholder="输入泰博出行商家版注册手机号/门店名称"
          value="{{searchQuery}}"
          bind:change="onSearchInput"
          bind:submit="onSearch"
          bind:clear="onClear"
        />
      </view>
      <t-button class="add-button" theme="primary" bind:tap="navigateToAddTerminal">新增</t-button>
    </view>
  </t-sticky>

  <view wx:if="{{terminals.length === 0}}">
    <view class="no-data">暂无数据</view>
  </view>
  <scroll-view scroll-y class="scroll-view" bindscrolltolower="onScrollToLower">
    <block wx:for="{{terminals}}" wx:key="id">
      <view class="terminal-item" bindtap="onSelectTerminal" data-terminal="{{item}}">
        <view class="terminal-info">
          <view class="item">
            <text class="label">终端编号:</text>
            <text class="value">{{item.id}}</text>
          </view>
          <view class="item">
            <text class="label">终端名称:</text>
            <text class="value">{{item.shopName}}</text>
          </view>
          <view class="item">
            <text class="label">终端地址:</text>
            <text class="value"
              >{{item.areaName + item.address + '(联系人：' + item.shopBossName + ' 联系方式：' + item.phoneNumber +
              ')'}}</text
            >
          </view>
        </view>
      </view>
    </block>
    <view wx:if="{{showNoMoreData}}" class="no-more-data-modal">没有更多数据</view>
  </scroll-view>
</view>
