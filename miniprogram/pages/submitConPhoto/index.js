const config = require('../../config/index');
const { XMLParser } = require('fast-xml-parser');
import { POST } from '../../library/optimizer/request';
Page({
  data: {
    detail: {},
    detailVos: [],
    doors: [],
    rejectionReason: '',
    latitudeSg: '',
    longitudeSg: '',
    address: '',
    province: '',
    city: '',
    district: '',
    phtotConfig: {
      sourceType: ['camera'],
    },
    videoConfig: {
      sourceType: ['camera'],
    },
  },
  onLoad(options) {
    const itemStr = decodeURIComponent(options.act);
    const data = JSON.parse(itemStr);

    // 遍历 detailVos 数组，替换 picVoList 中的 imgPath 域名，并添加 uploadedImages 和 uploadedVideos 数组
    const updatedDetailVos = data.detailVos.map((detail) => {
      // 替换 picVoList 中的 imgPath 域名
      const updatedPicVoList = detail.picVoList.map((picVo) => {
        if (picVo.imgPath) {
          picVo.imgPath = picVo.imgPath.replace(/^https?:\/\/[^\/:]+(:\d+)?/, `${config.imgURL}`);
        }
        return picVo;
      });

      return {
        ...detail,
        picVoList: updatedPicVoList, // 更新 picVoList
        uploadedImages: [], // 新增 uploadedImages 数组
        uploadedVideos: [], // 新增 uploadedVideos 数组
      };
    });
    console.log(updatedDetailVos);
    // 将处理后的数据设置到页面的 data 中
    this.setData({
      detail: data,
      detailVos: updatedDetailVos,
    });

    this.getLocation(); // 调用 getLocation 方法
  },

  getLocation() {
    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        const { latitude, longitude } = res;
        this.reverseGeocode(latitude, longitude);
      },
      fail: () => {
        wx.showToast({
          title: '获取位置失败',
          icon: 'none',
        });
      },
    });
  },

  reLocate() {
    this.getLocation();
  },

  reverseGeocode(latitude, longitude) {
    const url = `https://api.map.baidu.com/reverse_geocoding/v3/?ak=${config.baiduMapKey}&output=json&coordtype=wgs84ll&location=${latitude},${longitude}`;
    wx.request({
      url,
      success: (res) => {
        if (res.data && res.data.result && res.data.result.formatted_address) {
          const addressComponent = res.data.result.addressComponent;
          this.setData({
            latitude: `${latitude}`,
            longitude: `${longitude}`,
            address: res.data.result.formatted_address,
            province: addressComponent.province,
            city: addressComponent.city,
            district: addressComponent.district,
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '获取地址失败',
          icon: 'none',
        });
      },
    });
  },

  handleAdd(e) {
    const { id } = e.currentTarget.dataset;
    const { files } = e.detail;
    files.forEach((file) => this.onUpload(file, id));
  },

  handleVideoAdd(e) {
    const { id } = e.currentTarget.dataset;
    const { files } = e.detail;
    files.forEach((file) => this.onUpload(file, id, 'video'));
  },

  onUpload(file, id, type = 'photo') {
    const detailVos = this.data.detailVos;
    const detailIndex = detailVos.findIndex((detail) => detail.id === id);
    if (detailIndex === -1) return; // 如果找不到对应的 detail，退出函数

    const fileIndex =
      type === 'photo' ? detailVos[detailIndex].uploadedImages.length : detailVos[detailIndex].uploadedVideos.length;
    const newFile = {
      ...file,
      status: 'loading',
    };

    if (type === 'photo') {
      detailVos[detailIndex].uploadedImages.push(newFile);
    } else {
      detailVos[detailIndex].uploadedVideos.push(newFile);
    }

    this.setData({
      detailVos,
    });

    // 获取当前用户的用户名
    const userName = wx.getStorageSync('userData')?.businessObject?.frUserName;
    // 获取当前时间戳
    const timestamp = Date.now();
    // 生成新的文件名
    const newFileName = `${userName}_${timestamp}.${type === 'photo' ? 'jpg' : 'mp4'}`;

    // 获取文件系统管理器
    const fileSystemManager = wx.getFileSystemManager();

    // 压缩文件
    const compressFile = () => {
      if (type === 'photo') {
        return new Promise((resolve, reject) => {
          wx.compressImage({
            src: file.url,
            quality: 80, // 压缩质量
            success: resolve,
            fail: reject,
          });
        });
      } else {
        return new Promise((resolve, reject) => {
          wx.compressVideo({
            src: file.url,
            quality: 'high', // 压缩质量，可以是 'low', 'medium', 'high'
            success: resolve,
            fail: reject,
          });
        });
      }
    };

    // 压缩文件并上传
    compressFile()
      .then((compressedFile) => {
        // 新文件路径
        const newFilePath = `${wx.env.USER_DATA_PATH}/${newFileName}`;
        // 重命名压缩后的文件
        fileSystemManager.copyFile({
          srcPath: compressedFile.tempFilePath,
          destPath: newFilePath,
          success: () => {
            // 上传重命名后的文件
            const task = wx.uploadFile({
              url: `${config.defaultURL}/uploadServlet`, // 修改为实际的接口地址
              filePath: newFilePath, // 使用重命名后的文件路径
              name: 'file', // 文件的字段名
              formData: {
                realPath: newFilePath, // 使用重命名后的文件路径作为实际路径
              },
              success: (uploadRes) => {
                // 解析 XML 响应
                const xmlStr = uploadRes.data;
                const parser = new XMLParser();
                const result = parser.parse(xmlStr);
                const returncode = result.response.returncode;
                if (returncode == 100) {
                  // 上传成功，更新文件状态
                  if (type === 'photo') {
                    detailVos[detailIndex].uploadedImages[fileIndex].status = 'done';
                    detailVos[detailIndex].uploadedImages[fileIndex].url = newFilePath;
                  } else {
                    detailVos[detailIndex].uploadedVideos[fileIndex].status = 'done';
                    detailVos[detailIndex].uploadedVideos[fileIndex].url = newFilePath;
                  }
                  this.setData({
                    detailVos,
                  });
                } else {
                  // 上传失败，更新文件状态
                  if (type === 'photo') {
                    detailVos[detailIndex].uploadedImages[fileIndex].status = 'error';
                  } else {
                    detailVos[detailIndex].uploadedVideos[fileIndex].status = 'error';
                  }
                  this.setData({
                    detailVos,
                  });
                  console.error('Upload failed with returncode:', returncode);
                }
              },
              fail: (err) => {
                // 上传失败，更新文件状态
                uploadError(type, detailVos, detailIndex, fileIndex);
                console.error('Upload failed:', err);
              },
            });

            task.onProgressUpdate((res) => {
              if (type === 'photo') {
                detailVos[detailIndex].uploadedImages[fileIndex].percent = res.progress;
              } else {
                detailVos[detailIndex].uploadedVideos[fileIndex].percent = res.progress;
              }
              this.setData({
                detailVos,
              });
            });
          },
          fail: (err) => {
            if (type === 'photo') {
              detailVos[detailIndex].uploadedImages[fileIndex].status = 'error';
            } else {
              detailVos[detailIndex].uploadedVideos[fileIndex].status = 'error';
            }
            this.setData({
              detailVos,
            });
            console.error('Rename file failed:', err);
          },
        });
      })
      .catch((err) => {
        if (type === 'photo') {
          detailVos[detailIndex].uploadedImages[fileIndex].status = 'error';
        } else {
          detailVos[detailIndex].uploadedVideos[fileIndex].status = 'error';
        }
        this.setData({
          detailVos,
        });
        console.error('Compress file failed:', err);
      });
  },

  handleRemove(e) {
    const { index } = e.detail;
    const { id } = e.currentTarget.dataset;
    const detailVos = this.data.detailVos;
    const detailIndex = detailVos.findIndex((detail) => detail.id === id);
    if (detailIndex === -1) return; // 如果找不到对应的detail，退出函数
    detailVos[detailIndex].uploadedImages.splice(index, 1);
    this.setData({
      detailVos,
    });
  },

  handleVideoRemove(e) {
    const { index } = e.detail;
    const { id } = e.currentTarget.dataset;
    const detailVos = this.data.detailVos;
    const detailIndex = detailVos.findIndex((detail) => detail.id === id);
    if (detailIndex === -1) return; // 如果找不到对应的detail，退出函数
    detailVos[detailIndex].uploadedVideos.splice(index, 1);
    this.setData({
      detailVos,
    });
  },

  reviewImg(e) {
    wx.previewImage({
      urls: [e.currentTarget.dataset.url],
    });
  },

  previewVideo(e) {
    const current = e.target.dataset.src;
    wx.previewMedia({
      sources: this.data.uploadedVideos.map((item) => ({
        url: item.url,
        type: 'video',
      })),
      current: current,
    });
  },
  validateUploads() {
    const { detailVos } = this.data;
    for (const detail of detailVos) {
      if (detail.uploadedImages.length === 0 || detail.uploadedVideos.length === 0) {
        return false;
      }
      for (const image of detail.uploadedImages) {
        if (image.status !== 'done') {
          return false;
        }
      }
      for (const video of detail.uploadedVideos) {
        if (video.status !== 'done') {
          return false;
        }
      }
    }
    return true;
  },

  onSubmit() {
    if (!this.validateUploads()) {
      wx.showToast({
        title: '请上传所有照片和视频',
        icon: 'none',
      });
      return;
    }

    const { detailVos, latitudeSg, longitudeSg, address } = this.data;

    // 构建picVoList
    const picVoList = detailVos.reduce((list, detail) => {
      const images = detail.uploadedImages.map((img) => ({
        businessid: detail.id,
        pstime: this.getCurrentDate(),
        uploadtime: this.getCurrentDateTime(),
        userId: wx.getStorageSync('userData')?.businessObject?.frUserName,
        state: 0,
        type: '155',
        id: `${Date.now()}`,
        path: img.url,
        imgPath: img.url,
        imgType: '155',
        imgedate: this.getCurrentDate(),
        psTime: this.getCurrentDateTime(),
        uaccount: wx.getStorageSync('userData')?.businessObject?.frUserName,
      }));
      const videos = detail.uploadedVideos.map((video) => ({
        businessid: detail.id,
        pstime: this.getCurrentDate(),
        uploadtime: this.getCurrentDateTime(),
        userId: wx.getStorageSync('userData')?.businessObject?.frUserName,
        state: 0,
        type: '161',
        id: `${Date.now()}`,
        path: video.url,
        imgPath: video.url,
        imgType: '161',
        imgedate: this.getCurrentDate(),
        psTime: this.getCurrentDateTime(),
        uaccount: wx.getStorageSync('userData')?.businessObject?.frUserName,
      }));
      return list.concat(images, videos);
    }, []);

    const params = {
      businessId: this.data.detail.businessId,
      picVoListJson: JSON.stringify(picVoList),
      latitudeSg,
      longitudeSg,
      province: this.data.province,
      city: this.data.city,
      area: this.data.district,
      processInstanceId: this.data.detail.processInstanceId,
      hiddenLatitude: latitudeSg,
      hiddenLongitude: longitudeSg,
      hiddenAddress: address,
      constructionAddress: address,
      taskId: this.data.detail.taskId,
      actType: 2,
      id: this.data.detail.id,
    };

    POST(`/tsActAddressAndExecuteController.do?saveExecuteBpm`, params, {
      custom: {
        isLoading: true,
        msg: '保存中...',
        toast: true,
        catch: true,
      },
    })
      .then((res) => {
        wx.showToast({
          title: '提交成功',
          icon: 'success',
          duration: 2000, // 显示时间
          success: function () {
            setTimeout(() => {
              wx.navigateBack();
            }, 2000); // 等待提示框显示完毕后再返回上一页
          },
        });
      })
      .catch((err) => {
        wx.showToast({
          title: err.head ? err.head.message : '提交失败',
          icon: 'none',
        });
      });
  },

  getCurrentDate() {
    const now = new Date();
    return `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now
      .getDate()
      .toString()
      .padStart(2, '0')}`;
  },

  getCurrentDateTime() {
    const now = new Date();
    return `${now.getFullYear()}-${(now.getMonth() + 1)
      .toString()
      .padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')} ${now
      .getHours()
      .toString()
      .padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now
      .getSeconds()
      .toString()
      .padStart(2, '0')}`;
  },
});
